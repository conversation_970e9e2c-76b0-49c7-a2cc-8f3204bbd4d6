import { useRef, useEffect, useState } from 'react';
import * as PIXI from 'pixi.js';

// 🎨 PixiJS 高性能工况图组件
const Lmh2 = () => {
    const canvasRef = useRef(null);
    const appRef = useRef(null);
    const mainContainerRef = useRef(null); // 保存主容器引用
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
    const [isInitialized, setIsInitialized] = useState(false); // 标记是否已初始化

    // 监听容器尺寸变化
    useEffect(() => {
        const updateDimensions = () => {
            if (canvasRef.current) {
                const { clientWidth, clientHeight } = canvasRef.current;
                setDimensions({ width: clientWidth, height: clientHeight });
            }
        };

        // 初始化尺寸
        updateDimensions();

        // 监听窗口大小变化
        window.addEventListener('resize', updateDimensions);

        // 使用 ResizeObserver 监听容器大小变化
        const resizeObserver = new ResizeObserver(updateDimensions);
        if (canvasRef.current) {
            resizeObserver.observe(canvasRef.current);
        }

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
        };
    }, []);

    // 🎯 初始化 PIXI 应用（只执行一次）
    useEffect(() => {
        if (dimensions.width === 0 || dimensions.height === 0) return;
        if (isInitialized) return; // 防止重复初始化

        // 创建 PIXI 应用实例 - 使用新的 PixiJS 8.x API
        const app = new PIXI.Application();

        // 初始化应用
        const initApp = async () => {
            try {
                await app.init({
                    width: dimensions.width,
                    height: dimensions.height,
                    backgroundColor: 0x000000, // 透明背景
                    backgroundAlpha: 0, // 设置背景透明度为0
                    antialias: true,
                    resolution: window.devicePixelRatio || 1,
                    autoDensity: true,
                });

                // 检查组件是否仍然挂载
                if (!canvasRef.current) {
                    app.destroy(true);
                    return;
                }

                appRef.current = app;

                // 清空容器内容
                if (canvasRef.current) {
                    canvasRef.current.innerHTML = '';
                }

                // 使用新的 canvas API 替代 view
                if (canvasRef.current && app.canvas) {
                    canvasRef.current.appendChild(app.canvas);
                    // 设置 canvas 样式以适应容器
                    app.canvas.style.width = '100%';
                    app.canvas.style.height = '100%';
                    app.canvas.style.objectFit = 'contain';
                }

                // 🎯 绘制工况图的各个组件（只绘制一次）
                drawFlowChart(app, dimensions);
                setIsInitialized(true); // 标记为已初始化
            } catch (error) {
                console.error('PIXI 应用初始化失败:', error);
                if (app) {
                    app.destroy(true);
                }
            }
        };

        initApp();

        // 清理函数
        return () => {
            if (appRef.current) {
                try {
                    // 确保正确清理
                    if (canvasRef.current && appRef.current.canvas && canvasRef.current.contains(appRef.current.canvas)) {
                        canvasRef.current.removeChild(appRef.current.canvas);
                    }
                    appRef.current.destroy(true, {
                        children: true,
                        texture: true,
                        baseTexture: true
                    });
                } catch (error) {
                    console.error('PIXI 应用清理失败:', error);
                } finally {
                    appRef.current = null;
                    mainContainerRef.current = null;
                    setIsInitialized(false);
                }
            }
        };
    }, [dimensions.width, dimensions.height, isInitialized]);

    // 🔄 处理尺寸变化（只更新缩放，不重新绘制）
    useEffect(() => {
        if (!isInitialized || !appRef.current || !mainContainerRef.current) return;
        if (dimensions.width === 0 || dimensions.height === 0) return;

        // 更新应用尺寸
        appRef.current.renderer.resize(dimensions.width, dimensions.height);

        // 确保 canvas 样式同步更新
        if (appRef.current.canvas) {
            appRef.current.canvas.style.width = '100%';
            appRef.current.canvas.style.height = '100%';
        }

        // 重新计算缩放比例
        updateContainerScale(mainContainerRef.current, dimensions);
    }, [dimensions, isInitialized]);

    // 🔄 更新容器缩放（用于尺寸变化时）
    const updateContainerScale = (mainContainer, dimensions) => {
        // 计算工况图的实际边界
        // 左侧：流量计在x=10，宽度160（-80到+80），所以左边界是10-80=-70
        // 右侧：压力计在x=1380，宽度160（-80到+80），所以右边界是1380+80=1460
        // 上下边界需要考虑新的管道系统：上方到y=60，下方到y=340
        const actualLeft = -70;   // 左侧流量计的左边界
        const actualRight = 1460; // 右侧压力计的右边界
        const actualTop = 60;     // 上方管道的上边界
        const actualBottom = 340; // 下方管道的下边界

        const actualWidth = actualRight - actualLeft;   // 1410
        const actualHeight = actualBottom - actualTop;  // 230

        // 添加边距，确保工况图不会贴边
        const padding = 20; // 20px 边距
        const availableWidth = dimensions.width - padding * 2;
        const availableHeight = dimensions.height - padding * 2;

        // 计算缩放比例，确保工况图完全显示在可视范围内
        const scaleX = availableWidth / actualWidth;
        const scaleY = availableHeight / actualHeight;
        const scale = Math.min(scaleX, scaleY, 1); // 限制最大缩放为1，避免过度放大

        // 确保缩放值为正数
        const finalScale = Math.max(scale, 0.1); // 最小缩放为0.1，避免过小

        // 更新缩放
        mainContainer.scale.set(finalScale);

        // 计算实际显示尺寸
        const scaledWidth = actualWidth * finalScale;
        const scaledHeight = actualHeight * finalScale;

        // 重新居中显示 - 考虑实际边界偏移
        const offsetX = actualLeft * finalScale; // 左边界偏移
        const offsetY = actualTop * finalScale;  // 上边界偏移

        mainContainer.x = (dimensions.width - scaledWidth) / 2 - offsetX;
        mainContainer.y = (dimensions.height - scaledHeight) / 2 - offsetY;
    };

    // 🎨 绘制完整的工况图（只执行一次）
    const drawFlowChart = (app, dimensions) => {
        // 创建主容器
        const mainContainer = new PIXI.Container();

        // 保存主容器引用，用于后续缩放更新
        mainContainerRef.current = mainContainer;

        // 应用初始缩放
        updateContainerScale(mainContainer, dimensions);

        app.stage.addChild(mainContainer);

        const graphics = new PIXI.Graphics();
        mainContainer.addChild(graphics);

        // 🔗 绘制主管道系统
        drawPipelineSystem(graphics);

        // 📊 绘制仪表组件
        drawInstruments(mainContainer);

        // ⚙️ 绘制阀门组件
        drawValves(mainContainer);
    };

    // 🔗 绘制管道系统
    const drawPipelineSystem = (graphics) => {
        // 使用新的线条样式API
        graphics.setStrokeStyle({
            width: 3,
            color: 0x00ff88,
            alpha: 1
        });

        // 移除主水平管道，让泵机与进出口容器保持在同一水平线

        // 左侧进口管道连接（横向布局）
        // 连接流量计到压力计（完全接触容器边线）
        graphics.moveTo(90, 200);  // 流量计右侧（10+80=90）
        graphics.lineTo(140, 200); // 压力计左侧（220-80=140）
        graphics.stroke();

        // 压力计右侧已经直接连接到主管道起点，无需额外连接线

        // 右侧出口管道连接（横向布局）
        // 主管道已经连接到出口流量计左侧，无需额外连接线

        // 连接出口流量计到压力计（完全接触容器边线）
        graphics.moveTo(1230, 200); // 出口流量计右侧（1150+80=1230）
        graphics.lineTo(1300, 200); // 出口压力计左侧（1380-80=1300）
        graphics.stroke();

        // 绘制泵机连接管道系统（上下连接线+横向连接线）
        const pumpColors = [
            0xff6b6b, // 红色 - 一号泵
            0x4ecdc4, // 青色 - 二号泵
            0x45b7d1, // 蓝色 - 三号泵
            0x96ceb4, // 绿色 - 四号泵
            0xfeca57, // 黄色 - 五号泵
            0xfd79a8  // 粉色 - 六号泵
        ];

        // 绘制每个泵机的上下连接线
        for (let i = 0; i < 6; i++) {
            const x = 435 + i * 100;
            const color = pumpColors[i];

            graphics.setStrokeStyle({ width: 2, color: color });

            // 上方连接线（缩小三分之一：140px）
            graphics.moveTo(x, 200);
            graphics.lineTo(x, 60);
            graphics.stroke();

            // 下方连接线（缩小三分之一：140px）
            graphics.moveTo(x, 200);
            graphics.lineTo(x, 340);
            graphics.stroke();
        }

        // 绘制上方横向连接线
        graphics.setStrokeStyle({ width: 2, color: 0x00ff88 }); // 绿色横线
        graphics.moveTo(435, 60);
        graphics.lineTo(935, 60);
        graphics.stroke();

        // 绘制下方横向连接线
        graphics.setStrokeStyle({ width: 2, color: 0xff9500 }); // 橙色横线
        graphics.moveTo(435, 340);
        graphics.lineTo(935, 340);
        graphics.stroke();

        // 绘制左侧双弧线连接（进口压力计右侧 → 顶部绿线左侧）
        graphics.setStrokeStyle({ width: 2, color: 0x00ff88 }); // 绿色

        const startX1 = 300;  // 进口压力计右侧（220+80）
        const startY1 = 200;  // 压力计高度
        const endX1 = 435;    // 绿线左端
        const targetY1 = 60;  // 绿线高度
        const radius1 = 15;   // 圆角半径

        // 计算转折点
        const corner1X = 360; // 第一个转折点X

        // 路径：右→上→右
        graphics.moveTo(startX1, startY1);
        graphics.lineTo(corner1X - radius1, startY1);
        graphics.arcTo(corner1X, startY1, corner1X, startY1 - radius1, radius1);
        graphics.lineTo(corner1X, targetY1 + radius1);
        graphics.arcTo(corner1X, targetY1, corner1X + radius1, targetY1, radius1);
        graphics.lineTo(endX1, targetY1);
        graphics.stroke();

        // 绘制右侧双弧线连接（底部橙色线右侧 → 出口流量计左侧）
        graphics.setStrokeStyle({ width: 2, color: 0xff9500 }); // 橙色

        const startX2 = 935;  // 橙色线右端
        const startY2 = 340;  // 橙色线高度
        const endX2 = 1070;   // 出口流量计左侧（1150-80）
        const targetY2 = 200; // 流量计高度
        const radius2 = 15;   // 圆角半径

        // 计算转折点（与左侧保持一致的样式）
        const corner2X = 1000; // 第一个转折点X

        // 路径：右→上→右（与左侧一致的方向）
        graphics.moveTo(startX2, startY2);
        graphics.lineTo(corner2X - radius2, startY2);
        graphics.arcTo(corner2X, startY2, corner2X, startY2 - radius2, radius2);
        graphics.lineTo(corner2X, targetY2 + radius2);
        graphics.arcTo(corner2X, targetY2, corner2X + radius2, targetY2, radius2);
        graphics.lineTo(endX2, targetY2);
        graphics.stroke();

        // 绘制连接节点
        graphics.setStrokeStyle({ width: 0 });
        graphics.setFillStyle({ color: 0x00ff88 });

        // 移除所有连接点，保持简洁的设计
        const connectionPoints = [];

        connectionPoints.forEach(([x, y]) => {
            graphics.circle(x, y, 4);
            graphics.fill();
        });
    };

    // 📊 绘制仪表组件
    const drawInstruments = (mainContainer) => {
        // 🌊 左侧进口流量计（向左移动增加间距）
        createFlowMeter(mainContainer, 10, 200, '进口流量计');

        // 🔽 左侧进口压力计（横向布局，与流量计同一水平线，保持距离）
        createPressureMeter(mainContainer, 220, 200, '进口压力计');

        // 🌊 右侧出口流量计（横向布局，与进口保持一致，去掉数据）
        createFlowMeter(mainContainer, 1150, 200, '出口流量计');

        // 🔽 右侧出口压力计（向右移动增加间距）
        createPressureMeter(mainContainer, 1380, 200, '出口压力计');
    };

    // ⚙️ 绘制阀门组件
    const drawValves = (mainContainer) => {
        const valveNames = ['一号泵', '二号泵', '三号泵', '四号泵', '五号泵', '六号泵'];

        valveNames.forEach((name, index) => {
            // 计算居中位置：主管道从305到1065，中心点是685，6个泵机总宽度500，起始位置435
            const x = 435 + index * 100; // 调整起始位置为435以实现居中
            const y = 200; // 与进出口容器保持在同一水平线
            createValve(mainContainer, x, y, name);
        });
    };

    // 🌊 创建流量计组件
    const createFlowMeter = (parentContainer, x, y, title, mainValue = '', unit = '', totalValue = '') => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框（稍微缩小宽度，透明背景）
        const bg = new PIXI.Graphics();
        bg.setStrokeStyle({ width: 1, color: 0x00ff88 });
        bg.roundRect(-80, -35, 160, 70, 8);
        bg.stroke();
        container.addChild(bg);

        // 流量计图标（调整位置适应新容器）
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x00ff88 });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 添加刻度线
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const startX = -50 + Math.cos(angle) * 10;
            const startY = Math.sin(angle) * 10;
            const endX = -50 + Math.cos(angle) * 12;
            const endY = Math.sin(angle) * 12;

            icon.setStrokeStyle({ width: 1, color: 0x1a1a1a });
            icon.moveTo(startX, startY);
            icon.lineTo(endX, endY);
            icon.stroke();
        }
        container.addChild(icon);

        // 主数值（只在有数据时显示）
        if (mainValue && unit) {
            const mainText = new PIXI.Text({
                text: `${mainValue} ${unit}`,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 14,
                    fill: 0x00ff88,
                    fontWeight: 'bold',
                }
            });
            mainText.x = -20;
            mainText.y = -10;
            container.addChild(mainText);
        }

        // 累计值（只在有数据时显示）
        if (totalValue) {
            const totalText = new PIXI.Text({
                text: totalValue,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 10,
                    fill: 0xcccccc,
                }
            });
            totalText.x = -20;
            totalText.y = 8;
            container.addChild(totalText);
        }

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 12, // 稍微增大字体
                fill: 0xffffff, // 修改为白色
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -55;
        container.addChild(titleText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    // 🔽 创建压力计组件
    const createPressureMeter = (parentContainer, x, y, title, value = '', unit = '') => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框（与流量计保持一致的尺寸：160x70，透明背景）
        const bg = new PIXI.Graphics();
        bg.setStrokeStyle({ width: 1, color: 0x4a9eff });
        bg.roundRect(-80, -35, 160, 70, 8);
        bg.stroke();
        container.addChild(bg);

        // 压力计图标（调整位置以适应新的容器尺寸）
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x4a9eff });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 指针
        icon.setStrokeStyle({ width: 2, color: 0x1a1a1a });
        icon.moveTo(-50, 0);
        icon.lineTo(-50 + 10, -6);
        icon.stroke();
        container.addChild(icon);

        // 数值文本（只在有数据时显示）
        if (value && unit) {
            const valueText = new PIXI.Text({
                text: `${value} ${unit}`,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 14,
                    fill: 0x4a9eff,
                    fontWeight: 'bold',
                }
            });
            valueText.x = -15;
            valueText.y = -10;
            container.addChild(valueText);
        }

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 12, // 稍微增大字体
                fill: 0xffffff, // 修改为白色
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -55;
        container.addChild(titleText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    // ⚙️ 创建阀门组件
    const createValve = (parentContainer, x, y, name) => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 阀门背景
        const bg = new PIXI.Graphics();
        bg.setFillStyle({ color: 0x141414, alpha: 0.9 }); // 修改背景色为#141414
        bg.setStrokeStyle({ width: 1, color: 0xffffff }); // 边线修改细一点
        bg.roundRect(-30, -30, 60, 60, 8);
        bg.fill();
        bg.stroke();
        container.addChild(bg);

        // 泵机风扇（十字形状）
        const fan = new PIXI.Graphics();
        fan.setStrokeStyle({ width: 3, color: 0xffffff });

        // 绘制十字形状
        // 水平线
        fan.moveTo(-15, 0);
        fan.lineTo(15, 0);
        fan.stroke();

        // 垂直线
        fan.moveTo(0, -15);
        fan.lineTo(0, 15);
        fan.stroke();

        container.addChild(fan);

        // 中心圆点
        const center = new PIXI.Graphics();
        center.setFillStyle({ color: 0xffffff });
        center.circle(0, 0, 2);
        center.fill();
        container.addChild(center);

        // 阀门名称
        const nameText = new PIXI.Text({
            text: name,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xffffff,
            }
        });
        nameText.x = -nameText.width / 2;
        nameText.y = 40;
        container.addChild(nameText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    return (
        <div className="w-full h-full relative">
            <div
                ref={canvasRef}
                className="w-full h-full"
            />
        </div>
    );
};

export default Lmh2;
